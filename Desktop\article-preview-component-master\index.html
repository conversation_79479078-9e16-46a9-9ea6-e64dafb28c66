<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- displays site properly based on user's device -->

  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  
  <title>Frontend Mentor | Article preview component</title>

  <!-- Feel free to remove these styles or customise in your own stylesheet 👍 -->
  <style>
    body {
      font-family: 'Manrope', sans-serif;
      font-size: 13px;
      color: hsl(217, 19%, 35%);
      background-color: hsl(210, 46%, 95%);
      margin: 0;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .attribution { font-size: 12px; text-align: center; margin-top: auto;}
    .attribution a { color: hsl(228, 45%, 44%); }
    .article-preview {
      display: flex;
      flex-direction: column;
      max-width: 375px;
      background: white;
      height: 310px;
      border-radius: 5px;
      overflow: hidden;
      margin-left: 300px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .article-image {
      width: 100%;
      height: auto;
    }
    .article-content {
      padding: 20px;
    }
    .article-title {
      font-weight: 700;
      margin-bottom: 10px;
    }
    .article-text {
      color: hsl(214, 17%, 51%);
      margin-bottom: 20px;
    }
    .article-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
    }
    .author {
      display: flex;
      align-items: center;
    }
    .author img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
    }
    .author-info {
      display: flex;
      flex-direction: column;
    }
    .author-name {
      font-weight: 700;
    }
    .author-date {
      color: hsl(212, 23%, 69%);
    }
    .share-button {
      background: none;
      border: none;
      cursor: pointer;
    }
    .share-button img {
      width: 20px;
      height: 20px;
    }
    .share-tooltip {
      display: none;
      position: absolute;
      bottom: 60px;
      right: 0;
      background: hsl(217, 19%, 35%);
      color: white;
      border-radius: 10px;
      padding: 16px 32px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.15);
      align-items: center;
      gap: 16px;
      z-index: 10;
      font-size: 13px;
      letter-spacing: 5px;
      display: flex;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s;
    }
    .share-tooltip.active {
      opacity: 1;
      pointer-events: auto;
    }
    .share-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      right: 32px;
      border-width: 8px;
      border-style: solid;
      border-color: hsl(217, 19%, 35%) transparent transparent transparent;
    }
    .share-tooltip span {
      color: hsl(212, 23%, 69%);
      font-weight: 700;
      margin-right: 16px;
      letter-spacing: 5px;
    }
    .share-icons img {
      width: 20px;
      height: 20px;
      margin-right: 16px;
      cursor: pointer;
      filter: brightness(0) invert(1);
      transition: filter 0.2s;
    }
    .share-icons img:last-child {
      margin-right: 0;
    }
    @media (min-width: 900px) {
      .article-preview {
        flex-direction: row;
        max-width: 730px;
        min-width: 600px;
        min-height: 280px;
      }
      .article-image {
        width: 285px;
        height: 100%;
        object-fit: cover;
      }
      .article-content {
        width: calc(100% - 285px);
        padding: 40px 40px 32px 40px;
      }
    }
  </style>
</head>
<body>
  <div class="article-preview">
    <img src="./images/drawers.jpg" alt="Drawers" class="article-image">
    <div class="article-content">
      <h1 class="article-title">Shift the overall look and feel by adding these wonderful touches to furniture in your home</h1>
      <p class="article-text">Ever been in a room and felt like something was missing? Perhaps it felt slightly bare and uninviting. I've got some simple tips to help you make any room feel complete.</p>
      <div class="article-footer">
        <div class="author">
          <img src="./images/avatar-michelle.jpg" alt="Michelle Appleton">
          <div class="author-info">
            <span class="author-name">Michelle Appleton</span>
            <span class="author-date">28 Jun 2020</span>
          </div>
        </div>
        <button class="share-button" onclick="toggleShare(event)">
          <img src="./images/icon-share.svg" alt="Share">
        </button>
        <div class="share-tooltip" id="shareTooltip">
          <span>SHARE</span>
          <div class="share-icons">
            <img src="./images/icon-facebook.svg" alt="Facebook">
            <img src="./images/icon-twitter.svg" alt="Twitter">
            <img src="./images/icon-pinterest.svg" alt="Pinterest">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="attribution">
    Challenge by <a href="https://www.frontendmentor.io?ref=challenge" target="_blank">Frontend Mentor</a>. 
    Coded by <a href="#">Ayokanmi Adejola</a>.
  </div>
  <script>
    function toggleShare(e) {
      e.stopPropagation();
      const tooltip = document.getElementById('shareTooltip');
      tooltip.classList.toggle('active');
      // Hide tooltip when clicking outside
      if (tooltip.classList.contains('active')) {
        document.addEventListener('click', hideTooltipOnClick);
      } else {
        document.removeEventListener('click', hideTooltipOnClick);
      }
    }
    function hideTooltipOnClick(e) {
      const tooltip = document.getElementById('shareTooltip');
      if (!tooltip.contains(e.target)) {
        tooltip.classList.remove('active');
        document.removeEventListener('click', hideTooltipOnClick);
      }
    }
  </script>
</body>
</html>