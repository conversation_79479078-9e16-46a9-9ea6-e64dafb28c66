<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- displays site properly based on user's device -->

  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@500;700&display=swap" rel="stylesheet">

  <title>Frontend Mentor | Article preview component</title>

  <style>
    body {
      font-family: 'Manrope', sans-serif;
      font-size: 13px;
      color: hsl(217, 19%, 35%);
      background-color: hsl(210, 46%, 95%);
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .attribution {
      font-size: 11px;
      text-align: center;
      margin-top: 20px;
    }
    .attribution a { color: hsl(228, 45%, 44%); }
    .article-preview {
      display: flex;
      flex-direction: column;
      max-width: 327px;
      background: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 40px 40px -10px rgba(201, 213, 225, 0.5);
    }
    .article-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }
    .article-content {
      padding: 36px 32px 20px 32px;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }
    .article-title {
      font-weight: 700;
      font-size: 16px;
      line-height: 1.5;
      margin: 0 0 12px 0;
      color: hsl(217, 19%, 35%);
    }
    .article-text {
      color: hsl(214, 17%, 51%);
      font-size: 13px;
      line-height: 1.54;
      margin: 0 0 32px 0;
      flex-grow: 1;
    }
    .article-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      padding: 0 32px 20px 32px;
    }
    .author {
      display: flex;
      align-items: center;
    }
    .author img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 16px;
    }
    .author-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
    .author-name {
      font-weight: 700;
      font-size: 13px;
      color: hsl(217, 19%, 35%);
    }
    .author-date {
      color: hsl(212, 23%, 69%);
      font-size: 13px;
    }
    .share-button {
      background: hsl(210, 46%, 95%);
      border: none;
      cursor: pointer;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease;
    }
    .share-button:hover {
      background: hsl(217, 19%, 35%);
    }
    .share-button:hover svg path {
      fill: white;
    }
    .share-button svg {
      width: 15px;
      height: 13px;
    }
    .share-tooltip {
      position: absolute;
      bottom: 80px;
      left: 50%;
      transform: translateX(-50%);
      background: hsl(217, 19%, 35%);
      color: white;
      border-radius: 10px;
      padding: 18px 36px;
      box-shadow: 0 10px 10px rgba(201, 213, 225, 0.5);
      display: flex;
      align-items: center;
      gap: 16px;
      z-index: 10;
      font-size: 13px;
      letter-spacing: 5px;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease;
      white-space: nowrap;
    }
    .share-tooltip.active {
      opacity: 1;
      pointer-events: auto;
    }
    .share-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border-width: 12px;
      border-style: solid;
      border-color: hsl(217, 19%, 35%) transparent transparent transparent;
    }
    .share-tooltip span {
      color: hsl(212, 23%, 69%);
      font-weight: 500;
      margin-right: 5px;
      letter-spacing: 5px;
      font-size: 13px;
    }
    .share-icons {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    .share-icons svg {
      width: 20px;
      height: 20px;
      cursor: pointer;
      transition: transform 0.2s ease;
    }
    .share-icons svg:hover {
      transform: scale(1.1);
    }
    /* Mobile-specific styles */
    @media (max-width: 767px) {
      .share-tooltip {
        bottom: 70px;
        width: 248px;
      }
    }

    @media (min-width: 768px) {
      .article-preview {
        flex-direction: row;
        max-width: 730px;
        height: 280px;
      }
      .article-image {
        width: 285px;
        height: 280px;
        object-fit: cover;
      }
      .article-content {
        padding: 32px 40px 32px 40px;
        width: calc(100% - 285px);
      }
      .article-title {
        font-size: 20px;
        line-height: 1.4;
        margin-bottom: 12px;
      }
      .article-text {
        margin-bottom: 20px;
      }
      .article-footer {
        padding: 0;
        margin-top: auto;
      }
      .share-tooltip {
        bottom: 90px;
        right: -68px;
        left: auto;
        transform: none;
        width: auto;
      }
      .share-tooltip::after {
        left: 50%;
        transform: translateX(-50%);
      }
    }
  </style>
</head>
<body>
  <div class="article-preview">
    <img src="./images/drawers.jpg" alt="Drawers" class="article-image">
    <div class="article-content">
      <h1 class="article-title">Shift the overall look and feel by adding these wonderful touches to furniture in your home</h1>
      <p class="article-text">Ever been in a room and felt like something was missing? Perhaps it felt slightly bare and uninviting. I've got some simple tips to help you make any room feel complete.</p>
      <div class="article-footer">
        <div class="author">
          <img src="./images/avatar-michelle.jpg" alt="Michelle Appleton">
          <div class="author-info">
            <span class="author-name">Michelle Appleton</span>
            <span class="author-date">28 Jun 2020</span>
          </div>
        </div>
        <button class="share-button" onclick="toggleShare(event)">
          <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13"><path fill="#6E8098" d="M15 6.495L8.766.014V3.88H7.441C3.33 3.88 0 7.039 0 10.936v2.049l.589-.612C2.59 10.294 5.422 9.11 8.39 9.11h.375v3.867L15 6.495z"/></svg>
        </button>
        <div class="share-tooltip" id="shareTooltip">
          <span>SHARE</span>
          <div class="share-icons">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"><path fill="#FFF" d="M18.896 0H1.104C.494 0 0 .494 0 1.104v17.793C0 19.506.494 20 1.104 20h9.58v-7.745H8.076V9.237h2.606V7.01c0-2.583 1.578-3.99 3.883-3.99 1.104 0 2.052.082 2.329.119v2.7l-1.598.001c-1.254 0-1.496.597-1.496 1.47v1.928h2.989l-.39 3.018h-2.6V20h5.098c.608 0 1.102-.494 1.102-1.104V1.104C20 .494 19.506 0 18.896 0z"/></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17"><path fill="#FFF" d="M20 2.172a8.192 8.192 0 01-2.357.646 4.11 4.11 0 001.805-2.27 8.22 8.22 0 01-2.606.996A4.096 4.096 0 0013.847.248c-2.65 0-4.596 2.472-3.998 5.037A11.648 11.648 0 011.392 1a4.109 4.109 0 001.27 5.478 4.086 4.086 0 01-1.858-.513c-.045 1.9 1.318 3.679 3.291 4.075a4.113 4.113 0 01-1.853.07 4.106 4.106 0 003.833 2.849A8.25 8.25 0 010 14.658a11.616 11.616 0 006.29 1.843c7.618 0 11.923-6.434 11.663-12.205A8.354 8.354 0 0020 2.172z"/></svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"><path fill="#FFF" d="M10 0C4.477 0 0 4.477 0 10c0 4.237 2.636 7.855 6.356 9.312-.088-.791-.167-2.005.035-2.868.181-.78 1.172-4.97 1.172-4.97s-.299-.6-.299-1.486c0-1.39.806-2.428 1.81-2.428.852 0 1.264.64 1.264 1.408 0 .858-.546 2.14-.828 3.33-.236.995.5 1.807 1.48 1.807 1.778 0 3.144-1.874 3.144-4.58 0-2.393-1.72-4.068-4.176-4.068-2.845 0-4.516 2.135-4.516 4.34 0 .859.331 1.781.745 2.281a.3.3 0 01.069.288l-.278 1.133c-.044.183-.145.223-.335.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.052 6.608-6.052 3.469 0 6.165 2.473 6.165 5.776 0 3.447-2.173 6.22-5.19 6.22-1.013 0-1.97-.527-2.297-1.155l-.624 2.378c-.226.869-.835 1.958-1.244 2.621.937.29 1.931.446 2.962.446 5.523 0 10-4.477 10-10S15.523 0 10 0z"/></svg>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="attribution">
    Challenge by <a href="https://www.frontendmentor.io?ref=challenge" target="_blank">Frontend Mentor</a>. 
    Coded by <a href="#">Ayokanmi Adejola</a>.
  </div>
  <script>
    function toggleShare(e) {
      e.stopPropagation();
      const tooltip = document.getElementById('shareTooltip');
      tooltip.classList.toggle('active');
      // Hide tooltip when clicking outside
      if (tooltip.classList.contains('active')) {
        document.addEventListener('click', hideTooltipOnClick);
      } else {
        document.removeEventListener('click', hideTooltipOnClick);
      }
    }
    function hideTooltipOnClick(e) {
      const tooltip = document.getElementById('shareTooltip');
      if (!tooltip.contains(e.target)) {
        tooltip.classList.remove('active');
        document.removeEventListener('click', hideTooltipOnClick);
      }
    }
  </script>
</body>
</html>