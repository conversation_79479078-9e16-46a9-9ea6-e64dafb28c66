# Frontend Mentor - Article preview component solution

This is a solution to the [Article preview component challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/article-preview-component-dYBN_pYFT). Frontend Mentor challenges help you improve your coding skills by building realistic projects.

![Design preview for the Article preview component coding challenge](./design/desktop-preview.jpg)

## Table of contents

- [Overview](#overview)
  - [The challenge](#the-challenge)
  - [Screenshot](#screenshot)
  - [Links](#links)
- [My process](#my-process)
  - [Built with](#built-with)
  - [What I learned](#what-i-learned)
  - [Continued development](#continued-development)
  - [Useful resources](#useful-resources)
- [Author](#author)

## Overview

### The challenge

Users should be able to:

- View the optimal layout for the component depending on their device's screen size
- See the social media share links when they click the share icon

### Screenshot

![Desktop Design](./design/desktop-design.jpg)
![Mobile Design](./design/mobile-design.jpg)

### Links

- Solution URL: [GitHub Repository](https://github.com/yourusername/article-preview-component)
- Live Site URL: [Live Demo](https://yourusername.github.io/article-preview-component)

## My process

### Built with

- Semantic HTML5 markup
- CSS custom properties
- Flexbox
- CSS Grid
- Mobile-first workflow
- Vanilla JavaScript
- Responsive design principles

### What I learned

This project helped me practice several key frontend development concepts:

1. **Responsive Design**: Implementing a layout that works seamlessly across mobile and desktop devices using CSS media queries.

2. **Interactive Components**: Creating a share tooltip that appears/disappears with smooth transitions and proper event handling.

3. **CSS Positioning**: Using absolute positioning for the share tooltip with proper z-index management.

Here are some code snippets I'm particularly proud of:

```css
.share-tooltip {
  position: absolute;
  bottom: 60px;
  right: 0;
  background: hsl(217, 19%, 35%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
}

.share-tooltip.active {
  opacity: 1;
  pointer-events: auto;
}
```

```js
function toggleShare(e) {
  e.stopPropagation();
  const tooltip = document.getElementById('shareTooltip');
  tooltip.classList.toggle('active');

  if (tooltip.classList.contains('active')) {
    document.addEventListener('click', hideTooltipOnClick);
  } else {
    document.removeEventListener('click', hideTooltipOnClick);
  }
}
```

### Continued development

Areas I want to continue focusing on in future projects:

- **Accessibility**: Adding proper ARIA labels and keyboard navigation support
- **CSS Architecture**: Exploring methodologies like BEM for better CSS organization
- **JavaScript Frameworks**: Implementing similar components using React or Vue.js
- **Animation**: Adding more sophisticated animations and micro-interactions

### Useful resources

- [CSS-Tricks Flexbox Guide](https://css-tricks.com/snippets/css/a-guide-to-flexbox/) - This helped me understand flexbox layout for the responsive design.
- [MDN Web Docs - Event Handling](https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener) - Great resource for understanding event listeners and proper cleanup.

## Author

- Website - [Ayokanmi Adejola](https://www.your-site.com)
- Frontend Mentor - [@yourusername](https://www.frontendmentor.io/profile/yourusername)
- Twitter - [@yourusername](https://www.twitter.com/yourusername)
